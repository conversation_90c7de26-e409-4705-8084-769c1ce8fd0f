[remap]

importer="wavefront_obj"
importer_version=1
type="Mesh"
uid="uid://s3c660g525bx"
path="res://.godot/imported/House Interior Rooms - Free Sample-15.obj-86d4f69d333d8e5d922a2fd08736f7c0.mesh"

[deps]

files=["res://.godot/imported/House Interior Rooms - Free Sample-15.obj-86d4f69d333d8e5d922a2fd08736f7c0.mesh"]

source_file="res://assets/Free Sample/House Interior Rooms - Free Sample-15.obj"
dest_files=["res://.godot/imported/House Interior Rooms - Free Sample-15.obj-86d4f69d333d8e5d922a2fd08736f7c0.mesh", "res://.godot/imported/House Interior Rooms - Free Sample-15.obj-86d4f69d333d8e5d922a2fd08736f7c0.mesh"]

[params]

generate_tangents=true
generate_lods=true
generate_shadow_mesh=true
generate_lightmap_uv2=false
generate_lightmap_uv2_texel_size=0.2
scale_mesh=Vector3(1, 1, 1)
offset_mesh=Vector3(0, 0, 0)
force_disable_mesh_compression=false
