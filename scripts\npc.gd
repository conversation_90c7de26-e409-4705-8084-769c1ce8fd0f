# scripts/npc.gd
class_name NPC
extends CharacterBody3D

@export var npc_name: String = "NPC"
@export var move_speed: float = 3.0
@export var gold: int = 5

var current_target: Vector3
var is_moving: bool = false
var current_chair: Node3D = null
var is_sitting: bool = false

@onready var mesh_instance = $MeshInstance3D
@onready var chat_bubble = $ChatBubble
@onready var navigation_agent = $NavigationAgent3D

signal action_completed(npc: NPC, action: String)
signal chat_message(npc: NPC, message: String)

func _ready():
	navigation_agent.velocity_computed.connect(_on_velocity_computed)
	
func _physics_process(delta):
	if is_moving and not navigation_agent.is_navigation_finished():
		var next_path_position = navigation_agent.get_next_path_position()
		var new_velocity = global_position.direction_to(next_path_position) * move_speed
		
		if navigation_agent.avoidance_enabled:
			navigation_agent.set_velocity(new_velocity)
		else:
			_on_velocity_computed(new_velocity)

func _on_velocity_computed(safe_velocity: Vector3):
	velocity = safe_velocity
	move_and_slide()
	
	if navigation_agent.is_navigation_finished():
		is_moving = false
		action_completed.emit(self, "move_completed")

func move_to(target_position: Vector3):
	current_target = target_position
	navigation_agent.set_target_position(target_position)
	is_moving = true

func sit_on_chair(chair: Node3D):
	if chair and not is_sitting:
		current_chair = chair
		is_sitting = true
		global_position = chair.global_position + Vector3(0, 0.5, 0)
		action_completed.emit(self, "sat_on_chair")

func stand_up():
	if is_sitting:
		is_sitting = false
		current_chair = null
		action_completed.emit(self, "stood_up")

func show_chat(message: String):
	chat_message.emit(self, message)
	# TODO: Implement chat bubble UI

func get_position_grid() -> Vector2i:
	# Convert world position to grid coordinates
	var grid_manager = get_node("/root/Main/GridManager")
	var grid_pos = grid_manager.world_to_map(global_position)
	return Vector2i(grid_pos.x, grid_pos.z)
