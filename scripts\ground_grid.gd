# ground_grid.gd
@tool
extends MeshInstance3D

func _ready():
	# Call the update function when the game starts
	update_shader_params()

func _process(_delta):
	# This ensures the shader updates in the editor if you change properties
	if Engine.is_editor_hint():
		update_shader_params()

func update_shader_params():
	# Ensure we have a mesh and a material to work with
	if not self.mesh or not self.get_active_material(0):
		return

	# Check if the material is a ShaderMaterial
	var mat = self.get_active_material(0)
	if not mat is ShaderMaterial:
		return

	# Get the size of the mesh. The AABB (Axis-Aligned Bounding Box) gives us this.
	var mesh_size = self.mesh.get_aabb().size

	# The plane's size is on the X and Z axes.
	var scale_vector = Vector2(mesh_size.x, mesh_size.z)

	# Pass this size vector to the shader's "grid_scale" uniform.
	mat.set_shader_parameter("grid_scale", scale_vector)
