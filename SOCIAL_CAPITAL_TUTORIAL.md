# Social Capital Game Implementation Tutorial

This tutorial will guide you through implementing the "Social Capital" game mechanics on top of our existing grid-based building system. We'll build this in three phases as outlined in the plan.

## Overview

**Core Concept**: You are a Game Host running "Falling Furniture" - a musical chairs-like game where NPCs (powered by LLMs) compete for chairs. Your reputation depends on how fairly you run the game.

**Key Systems**:
- LLM-powered NPCs that understand context and react intelligently
- Event logging system for tracking all game actions
- Reputation system based on NPC evaluations
- Trading system for gold and staying in the game

---

## Phase 1: The Sentient Sandbox

### Goal
Create NPCs that can perceive their environment and act with purpose based on game rules.

### Step 1: Create the NPC System

First, let's create the core NPC class:

```gdscript
# scripts/npc.gd
class_name NPC
extends CharacterBody3D

@export var npc_name: String = "NPC"
@export var move_speed: float = 3.0
@export var gold: int = 5

var current_target: Vector3
var is_moving: bool = false
var current_chair: Node3D = null
var is_sitting: bool = false

@onready var mesh_instance = $MeshInstance3D
@onready var chat_bubble = $ChatBubble
@onready var navigation_agent = $NavigationAgent3D

signal action_completed(npc: NPC, action: String)
signal chat_message(npc: NPC, message: String)

func _ready():
    navigation_agent.velocity_computed.connect(_on_velocity_computed)
    
func _physics_process(delta):
    if is_moving and not navigation_agent.is_navigation_finished():
        var next_path_position = navigation_agent.get_next_path_position()
        var new_velocity = global_position.direction_to(next_path_position) * move_speed
        
        if navigation_agent.avoidance_enabled:
            navigation_agent.set_velocity(new_velocity)
        else:
            _on_velocity_computed(new_velocity)

func _on_velocity_computed(safe_velocity: Vector3):
    velocity = safe_velocity
    move_and_slide()
    
    if navigation_agent.is_navigation_finished():
        is_moving = false
        action_completed.emit(self, "move_completed")

func move_to(target_position: Vector3):
    current_target = target_position
    navigation_agent.set_target_position(target_position)
    is_moving = true

func sit_on_chair(chair: Node3D):
    if chair and not is_sitting:
        current_chair = chair
        is_sitting = true
        global_position = chair.global_position + Vector3(0, 0.5, 0)
        action_completed.emit(self, "sat_on_chair")

func stand_up():
    if is_sitting:
        is_sitting = false
        current_chair = null
        action_completed.emit(self, "stood_up")

func show_chat(message: String):
    chat_message.emit(self, message)
    # TODO: Implement chat bubble UI

func get_position_grid() -> Vector2i:
    # Convert world position to grid coordinates
    var grid_manager = get_node("/root/Main/GridManager")
    var grid_pos = grid_manager.world_to_map(global_position)
    return Vector2i(grid_pos.x, grid_pos.z)
```

### Step 2: Create the Game State Manager

```gdscript
# scripts/game_state_manager.gd
class_name GameStateManager
extends Node

signal game_event(event: String, data: Dictionary)

var npcs: Array[NPC] = []
var chairs: Array[Node3D] = []
var event_log: Array[String] = []
var game_active: bool = false
var round_number: int = 0

@onready var grid_manager = get_node("../GridManager")

func _ready():
    # Connect to building manager to track chair placement
    var building_manager = get_node("../BuildingManager")
    # We'll need to modify building manager to emit signals

func add_npc(npc: NPC):
    npcs.append(npc)
    npc.action_completed.connect(_on_npc_action_completed)
    npc.chat_message.connect(_on_npc_chat)
    log_event("npc_joined", {"name": npc.npc_name, "position": npc.get_position_grid()})

func remove_npc(npc: NPC):
    npcs.erase(npc)
    log_event("npc_left", {"name": npc.npc_name})

func add_chair(chair: Node3D):
    chairs.append(chair)
    var grid_pos = grid_manager.world_to_map(chair.global_position)
    log_event("host_placed_chair", {"position": Vector2i(grid_pos.x, grid_pos.z)})
    game_event.emit("chair_placed", {"chair": chair})

func remove_chair(chair: Node3D):
    chairs.erase(chair)
    var grid_pos = grid_manager.world_to_map(chair.global_position)
    log_event("host_removed_chair", {"position": Vector2i(grid_pos.x, grid_pos.z)})

func kick_npc(npc: NPC):
    log_event("host_kicked_npc", {"name": npc.npc_name})
    game_event.emit("npc_kicked", {"npc": npc})
    # TODO: Trigger exit interview

func log_event(event_type: String, data: Dictionary = {}):
    var timestamp = Time.get_unix_time_from_system()
    var event_string = event_type
    
    match event_type:
        "host_placed_chair":
            event_string = "Host placed chair at (%d,%d)" % [data.position.x, data.position.y]
        "npc_joined":
            event_string = "%s joined the room at (%d,%d)" % [data.name, data.position.x, data.position.y]
        "npc_moved":
            event_string = "%s moved to (%d,%d)" % [data.name, data.position.x, data.position.y]
        "npc_sat_on_chair":
            event_string = "%s sat on chair at (%d,%d)" % [data.name, data.position.x, data.position.y]
        "host_kicked_npc":
            event_string = "Host kicked %s" % data.name
    
    event_log.append(event_string)
    print("EVENT: ", event_string)
    
    # Keep only last 20 events for LLM context
    if event_log.size() > 20:
        event_log = event_log.slice(-20)

func _on_npc_action_completed(npc: NPC, action: String):
    match action:
        "move_completed":
            log_event("npc_moved", {"name": npc.npc_name, "position": npc.get_position_grid()})
        "sat_on_chair":
            log_event("npc_sat_on_chair", {"name": npc.npc_name, "position": npc.get_position_grid()})

func _on_npc_chat(npc: NPC, message: String):
    log_event("npc_chat", {"name": npc.npc_name, "message": message})

func generate_ascii_map() -> String:
    # Create a simple ASCII representation of the room
    var map_size = 10
    var ascii_map = []
    
    # Initialize empty map
    for y in range(map_size):
        var row = []
        for x in range(map_size):
            row.append(".")
        ascii_map.append(row)
    
    # Place chairs
    for chair in chairs:
        var grid_pos = grid_manager.world_to_map(chair.global_position)
        var x = clamp(grid_pos.x + map_size/2, 0, map_size-1)
        var y = clamp(grid_pos.z + map_size/2, 0, map_size-1)
        ascii_map[y][x] = "C"
    
    # Place NPCs
    for npc in npcs:
        var grid_pos = npc.get_position_grid()
        var x = clamp(grid_pos.x + map_size/2, 0, map_size-1)
        var y = clamp(grid_pos.y + map_size/2, 0, map_size-1)
        if ascii_map[y][x] == ".":
            ascii_map[y][x] = "N"
        elif ascii_map[y][x] == "C":
            ascii_map[y][x] = "S"  # Sitting on chair
    
    # Convert to string
    var result = ""
    for row in ascii_map:
        result += "".join(row) + "\n"
    
    return result

func get_recent_events(count: int = 5) -> Array[String]:
    var start_index = max(0, event_log.size() - count)
    return event_log.slice(start_index)
```

### Step 3: Create the LLM Integration System

```gdscript
# scripts/llm_manager.gd
class_name LLMManager
extends Node

const LLM_URL = "http://localhost:1234/v1/chat/completions"  # Adjust for your LLM setup

@onready var http_request = HTTPRequest.new()
@onready var game_state = get_node("../GameStateManager")

func _ready():
    add_child(http_request)
    http_request.request_completed.connect(_on_request_completed)

var pending_requests = {}  # Track which NPC each request is for

func generate_npc_action(npc: NPC):
    var prompt = create_context_prompt(npc)
    send_llm_request(npc, prompt)

func create_context_prompt(npc: NPC) -> String:
    var prompt = """You are %s. You are in a room to play 'Falling Furniture'. 

RULES: When the host places chairs, find an empty one and sit down as fast as possible. The last one standing is out. The host will kick the loser. The winner gets a prize.

CURRENT ROOM STATE:
%s

RECENT EVENTS:
%s

You can take ONE of these actions:
- MOVE_TO(x, y) - Move to grid position
- SIT_ON_CHAIR(x, y) - Sit on chair at position  
- CHAT("message") - Say something
- WAIT - Do nothing this turn

Respond with only: ACTION: [your_action]
""" % [npc.npc_name, game_state.generate_ascii_map(), "\n".join(game_state.get_recent_events())]
    
    return prompt

func send_llm_request(npc: NPC, prompt: String):
    var headers = ["Content-Type: application/json"]
    var body = JSON.stringify({
        "model": "local-model",
        "messages": [{"role": "user", "content": prompt}],
        "max_tokens": 100,
        "temperature": 0.7
    })
    
    var request_id = http_request.request(LLM_URL, headers, HTTPClient.METHOD_POST, body)
    pending_requests[request_id] = npc

func _on_request_completed(result: int, response_code: int, headers: PackedStringArray, body: PackedByteArray):
    if response_code != 200:
        print("LLM request failed: ", response_code)
        return
    
    var json = JSON.new()
    var parse_result = json.parse(body.get_string_from_utf8())
    
    if parse_result != OK:
        print("Failed to parse LLM response")
        return
    
    var response = json.data
    var content = response.choices[0].message.content
    
    # Find which NPC this response is for
    var npc = null
    for request_id in pending_requests:
        npc = pending_requests[request_id]
        pending_requests.erase(request_id)
        break
    
    if npc:
        parse_and_execute_action(npc, content)

func parse_and_execute_action(npc: NPC, response: String):
    # Extract action from response
    var action_regex = RegEx.new()
    action_regex.compile(r"ACTION:\s*(.+)")
    var result = action_regex.search(response)
    
    if not result:
        print("No valid action found in LLM response: ", response)
        return
    
    var action = result.get_string(1).strip_edges()
    execute_npc_action(npc, action)

func execute_npc_action(npc: NPC, action: String):
    print("%s wants to: %s" % [npc.npc_name, action])
    
    if action.begins_with("MOVE_TO"):
        var coords = extract_coordinates(action)
        if coords:
            var world_pos = game_state.grid_manager.map_to_world(Vector3i(coords.x, 0, coords.y))
            npc.move_to(world_pos)
    
    elif action.begins_with("SIT_ON_CHAIR"):
        var coords = extract_coordinates(action)
        if coords:
            var chair = find_chair_at_position(coords)
            if chair:
                npc.sit_on_chair(chair)
    
    elif action.begins_with("CHAT"):
        var message = extract_chat_message(action)
        if message:
            npc.show_chat(message)
    
    elif action == "WAIT":
        pass  # Do nothing

func extract_coordinates(action: String) -> Vector2i:
    var coord_regex = RegEx.new()
    coord_regex.compile(r"\((\d+),\s*(\d+)\)")
    var result = coord_regex.search(action)
    
    if result:
        return Vector2i(result.get_string(1).to_int(), result.get_string(2).to_int())
    return Vector2i.ZERO

func extract_chat_message(action: String) -> String:
    var chat_regex = RegEx.new()
    chat_regex.compile(r'CHAT\("([^"]+)"\)')
    var result = chat_regex.search(action)
    
    if result:
        return result.get_string(1)
    return ""

func find_chair_at_position(pos: Vector2i) -> Node3D:
    for chair in game_state.chairs:
        var chair_grid_pos = game_state.grid_manager.world_to_map(chair.global_position)
        if Vector2i(chair_grid_pos.x, chair_grid_pos.z) == pos:
            return chair
    return null
```

This covers the foundation for Phase 1. The NPCs now have:
- Contextual awareness of the room and game rules
- LLM-powered decision making
- Action parsing and execution
- Event logging system

**Next Steps**: 
1. Create NPC scenes with proper 3D models
2. Set up the LLM server (like LM Studio or Ollama)
3. Test that NPCs move with purpose when you place chairs
4. Add a timer system to regularly query NPCs for actions

---

## Phase 2: The Host's Toolkit & Emergent Game

### Goal
Enable the player to manually run "Falling Furniture" games with full NPC reactions.

### Step 4: Modify Building Manager for Chair Placement

First, let's extend our existing building system to support chair placement:

```gdscript
# Add to scripts/building_manager.gd

enum BuildMode { NONE, FLOOR, WALL, FURNITURE, CHAIR, DELETE }  # Add CHAIR mode

@onready var game_state_manager = get_node("../GameStateManager")
@export var chair_scene: PackedScene

func set_build_mode(mode: BuildMode):
    current_mode = mode
    print("Build mode set to: ", BuildMode.keys()[mode])

    # Set the appropriate highlighter mode
    if grid_manager:
        grid_manager.set_highlighter_mode(mode == BuildMode.WALL or mode == BuildMode.DELETE)

func _unhandled_input(_event):
    if not grid_manager.is_mouse_on_grid:
        return

    if current_mode == BuildMode.FLOOR:
        handle_floor_placement()
    elif current_mode == BuildMode.WALL:
        handle_wall_placement()
    elif current_mode == BuildMode.FURNITURE:
        handle_furniture_placement()
    elif current_mode == BuildMode.CHAIR:
        handle_chair_placement()  # New function
    elif current_mode == BuildMode.DELETE:
        handle_deletion()

func handle_chair_placement():
    if Input.is_action_just_pressed("place_object"):
        var map_pos = grid_manager.current_grid_pos

        # Check if position is valid (has floor, no existing furniture)
        if floor_data.has(map_pos) and not furniture_data.has(map_pos):
            var new_chair = chair_scene.instantiate()
            furniture_container.add_child(new_chair)
            new_chair.global_position = grid_manager.map_to_world(map_pos)
            new_chair.global_position.y = 0.1

            furniture_data[map_pos] = new_chair

            # Notify game state manager
            if game_state_manager:
                game_state_manager.add_chair(new_chair)
```

### Step 5: Create NPC Kicking System

```gdscript
# Add to scripts/building_manager.gd or create scripts/host_actions.gd

class_name HostActions
extends Node

@onready var game_state_manager = get_node("../GameStateManager")
@onready var camera = get_viewport().get_camera_3d()

func _unhandled_input(event):
    if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_RIGHT:
        # Right-click to kick NPCs
        handle_npc_kick()

func handle_npc_kick():
    var mouse_pos = get_viewport().get_mouse_position()
    var from = camera.project_ray_origin(mouse_pos)
    var to = from + camera.project_ray_normal(mouse_pos) * 1000

    var space_state = get_world_3d().direct_space_state
    var query = PhysicsRayQueryParameters3D.create(from, to)
    var result = space_state.intersect_ray(query)

    if result:
        var collider = result.collider
        var npc = collider.get_parent() if collider.get_parent() is NPC else null

        if npc:
            kick_npc(npc)

func kick_npc(npc: NPC):
    print("Kicking NPC: ", npc.npc_name)
    game_state_manager.kick_npc(npc)

    # Remove from scene after a delay
    await get_tree().create_timer(1.0).timeout
    npc.queue_free()
```

### Step 6: Create Trading System

```gdscript
# scripts/trading_system.gd
class_name TradingSystem
extends Node

signal trade_completed(trader_name: String, item: String, amount: int)

var player_gold: int = 100
var active_trades: Array[Dictionary] = []

@onready var game_state_manager = get_node("../GameStateManager")

func offer_trade_to_npc(npc: NPC, item: String, amount: int):
    var trade = {
        "npc": npc,
        "item": item,
        "amount": amount,
        "timestamp": Time.get_unix_time_from_system()
    }

    active_trades.append(trade)
    game_state_manager.log_event("host_offered_trade", {
        "npc_name": npc.npc_name,
        "item": item,
        "amount": amount
    })

func accept_npc_trade_offer(npc: NPC, item: String, amount: int) -> bool:
    if item == "gold" and player_gold >= amount:
        player_gold -= amount
        npc.gold += amount

        game_state_manager.log_event("host_traded_npc", {
            "npc_name": npc.npc_name,
            "item": item,
            "amount": amount
        })

        trade_completed.emit(npc.npc_name, item, amount)
        return true

    return false

func npc_offers_to_stay(npc: NPC, gold_amount: int) -> bool:
    # Called when NPC wants to pay to avoid being kicked
    if accept_npc_trade_offer(npc, "gold", gold_amount):
        game_state_manager.log_event("npc_paid_to_stay", {
            "npc_name": npc.npc_name,
            "amount": gold_amount
        })
        return true

    return false
```

---

## Phase 3: The Reputation & Consequence Engine

### Goal
Implement the reputation system that makes host actions matter long-term.

### Step 7: Create Reputation System

```gdscript
# scripts/reputation_system.gd
class_name ReputationSystem
extends Node

signal reputation_changed(old_value: int, new_value: int)

var host_reputation: int = 0
var reputation_history: Array[Dictionary] = []

@onready var game_state_manager = get_node("../GameStateManager")
@onready var llm_manager = get_node("../LLMManager")

func _ready():
    game_state_manager.game_event.connect(_on_game_event)

func _on_game_event(event: String, data: Dictionary):
    if event == "npc_kicked":
        conduct_exit_interview(data.npc)

func conduct_exit_interview(npc: NPC):
    var evaluation_prompt = create_evaluation_prompt(npc)
    send_evaluation_request(npc, evaluation_prompt)

func create_evaluation_prompt(npc: NPC) -> String:
    var full_event_log = "\n".join(game_state_manager.event_log)

    var prompt = """You have just left the game session. Here is the complete event log from your time in the room:

%s

Based on this log, please evaluate the host. Was the game fair? Were you cheated? Was the prize appropriate?

Respond ONLY with a JSON object containing:
- reputation_change: a number between -10 and +10
- justification: a short sentence explaining your rating

Example: {"reputation_change": -3, "justification": "Host kicked me unfairly when I reached the chair first"}
""" % full_event_log

    return prompt

func send_evaluation_request(npc: NPC, prompt: String):
    # Use similar HTTP request system as LLM manager
    var headers = ["Content-Type: application/json"]
    var body = JSON.stringify({
        "model": "local-model",
        "messages": [{"role": "user", "content": prompt}],
        "max_tokens": 150,
        "temperature": 0.3
    })

    var http_request = HTTPRequest.new()
    add_child(http_request)
    http_request.request_completed.connect(_on_evaluation_completed.bind(npc, http_request))
    http_request.request(llm_manager.LLM_URL, headers, HTTPClient.METHOD_POST, body)

func _on_evaluation_completed(npc: NPC, http_request: HTTPRequest, result: int, response_code: int, headers: PackedStringArray, body: PackedByteArray):
    http_request.queue_free()

    if response_code != 200:
        print("Evaluation request failed: ", response_code)
        return

    var json = JSON.new()
    var parse_result = json.parse(body.get_string_from_utf8())

    if parse_result != OK:
        print("Failed to parse evaluation response")
        return

    var response = json.data
    var content = response.choices[0].message.content

    parse_evaluation_response(npc, content)

func parse_evaluation_response(npc: NPC, response: String):
    var json = JSON.new()
    var parse_result = json.parse(response)

    if parse_result != OK:
        print("Failed to parse evaluation JSON: ", response)
        return

    var evaluation = json.data

    if evaluation.has("reputation_change") and evaluation.has("justification"):
        var change = int(evaluation.reputation_change)
        var justification = str(evaluation.justification)

        update_reputation(change, npc.npc_name, justification)

func update_reputation(change: int, npc_name: String, justification: String):
    var old_reputation = host_reputation
    host_reputation += change

    # Clamp reputation to reasonable bounds
    host_reputation = clamp(host_reputation, -100, 100)

    var reputation_entry = {
        "npc_name": npc_name,
        "change": change,
        "justification": justification,
        "timestamp": Time.get_unix_time_from_system(),
        "new_total": host_reputation
    }

    reputation_history.append(reputation_entry)

    print("Reputation changed by %d (from %s): %s" % [change, npc_name, justification])
    print("New reputation: %d" % host_reputation)

    reputation_changed.emit(old_reputation, host_reputation)

func get_reputation_description() -> String:
    if host_reputation >= 50:
        return "Excellent Host"
    elif host_reputation >= 20:
        return "Good Host"
    elif host_reputation >= 0:
        return "Fair Host"
    elif host_reputation >= -20:
        return "Questionable Host"
    else:
        return "Terrible Host"
```

### Step 8: Enhanced NPC Context with Reputation

Update the LLM Manager to include reputation in NPC decision-making:

```gdscript
# Add to scripts/llm_manager.gd

@onready var reputation_system = get_node("../ReputationSystem")

func create_context_prompt(npc: NPC) -> String:
    var reputation_desc = reputation_system.get_reputation_description()
    var reputation_score = reputation_system.host_reputation

    var prompt = """You are %s. You are entering a room hosted by the Game Host.

HOST REPUTATION: %s (Score: %d)
- Positive scores mean the host is fair and trustworthy
- Negative scores mean the host cheats and is unfair

GAME RULES:
- This is 'Falling Furniture' - musical chairs with elimination
- When chairs are placed, sit down as fast as possible
- Last one standing gets kicked out
- Winner gets a gold prize
- If you're about to be kicked, you can offer 1 Gold to stay (consider the host's reputation!)

CURRENT ROOM STATE:
%s

RECENT EVENTS:
%s

Available actions:
- MOVE_TO(x, y) - Move to grid position
- SIT_ON_CHAIR(x, y) - Sit on chair at position
- CHAT("message") - Say something
- OFFER_TRADE("gold", 1) - Pay 1 gold to avoid being kicked
- WAIT - Do nothing this turn

Consider the host's reputation when deciding whether to pay to stay!

Respond with only: ACTION: [your_action]
""" % [npc.npc_name, reputation_desc, reputation_score, game_state.generate_ascii_map(), "\n".join(game_state.get_recent_events())]

    return prompt

# Add new action parsing for trade offers
func execute_npc_action(npc: NPC, action: String):
    print("%s wants to: %s" % [npc.npc_name, action])

    if action.begins_with("MOVE_TO"):
        var coords = extract_coordinates(action)
        if coords:
            var world_pos = game_state.grid_manager.map_to_world(Vector3i(coords.x, 0, coords.y))
            npc.move_to(world_pos)

    elif action.begins_with("SIT_ON_CHAIR"):
        var coords = extract_coordinates(action)
        if coords:
            var chair = find_chair_at_position(coords)
            if chair:
                npc.sit_on_chair(chair)

    elif action.begins_with("CHAT"):
        var message = extract_chat_message(action)
        if message:
            npc.show_chat(message)

    elif action.begins_with("OFFER_TRADE"):
        handle_npc_trade_offer(npc, action)

    elif action == "WAIT":
        pass

func handle_npc_trade_offer(npc: NPC, action: String):
    # Parse OFFER_TRADE("gold", 1)
    var trade_regex = RegEx.new()
    trade_regex.compile(r'OFFER_TRADE\("([^"]+)",\s*(\d+)\)')
    var result = trade_regex.search(action)

    if result:
        var item = result.get_string(1)
        var amount = result.get_string(2).to_int()

        var trading_system = get_node("../TradingSystem")
        if trading_system and item == "gold" and npc.gold >= amount:
            var success = trading_system.npc_offers_to_stay(npc, amount)
            if success:
                npc.show_chat("I'll pay %d gold to stay!" % amount)
            else:
                npc.show_chat("I tried to pay but the host refused...")
```

### Step 9: Create Game UI

```gdscript
# scripts/game_ui.gd
extends Control

@onready var reputation_label = $VBoxContainer/ReputationLabel
@onready var gold_label = $VBoxContainer/GoldLabel
@onready var event_log = $VBoxContainer/EventLog
@onready var npc_count_label = $VBoxContainer/NPCCountLabel

@onready var reputation_system = get_node("../ReputationSystem")
@onready var trading_system = get_node("../TradingSystem")
@onready var game_state_manager = get_node("../GameStateManager")

func _ready():
    reputation_system.reputation_changed.connect(_on_reputation_changed)
    trading_system.trade_completed.connect(_on_trade_completed)
    game_state_manager.game_event.connect(_on_game_event)

    update_ui()

func _on_reputation_changed(old_value: int, new_value: int):
    update_ui()

func _on_trade_completed(trader_name: String, item: String, amount: int):
    update_ui()

func _on_game_event(event: String, data: Dictionary):
    update_ui()

func update_ui():
    reputation_label.text = "Reputation: %d (%s)" % [reputation_system.host_reputation, reputation_system.get_reputation_description()]
    gold_label.text = "Gold: %d" % trading_system.player_gold
    npc_count_label.text = "NPCs in Room: %d" % game_state_manager.npcs.size()

    # Update event log
    var recent_events = game_state_manager.get_recent_events(5)
    event_log.text = "\n".join(recent_events)
```

### Step 10: Integration and Setup

1. **Scene Structure**: Update your main scene to include:
   ```
   Main
   ├── GridManager
   ├── BuildingManager
   ├── GameStateManager (new)
   ├── LLMManager (new)
   ├── ReputationSystem (new)
   ├── TradingSystem (new)
   ├── HostActions (new)
   └── GameUI (new)
   ```

2. **NPC Scene**: Create `npc.tscn` with:
   - CharacterBody3D (root)
   - MeshInstance3D (with character model)
   - CollisionShape3D
   - NavigationAgent3D
   - ChatBubble (UI element)

3. **Chair Scene**: Create `chair.tscn` similar to existing furniture

4. **LLM Setup**: Install and configure a local LLM server:
   - LM Studio: https://lmstudio.ai/
   - Ollama: https://ollama.ai/
   - Or any OpenAI-compatible API

### Step 11: Testing the Complete Loop

1. **Start Game**: Spawn 3-4 NPCs in the room
2. **Place Chairs**: Use chair mode to place fewer chairs than NPCs
3. **Watch NPCs React**: They should move toward chairs intelligently
4. **Kick Someone**: Right-click an NPC to kick them
5. **Check Reputation**: See how the kicked NPC rates your fairness
6. **Repeat**: Run multiple rounds to build/lose reputation

### Key Features Implemented

✅ **LLM-Powered NPCs**: Context-aware, rule-understanding AI characters
✅ **Event Logging**: Complete tracking of all game actions
✅ **Reputation System**: NPCs judge your fairness and remember it
✅ **Trading System**: Gold economy with pay-to-stay mechanics
✅ **Host Tools**: Chair placement and NPC kicking
✅ **Emergent Gameplay**: Reputation affects NPC willingness to pay

### Next Steps for Polish

- Add visual feedback for NPC emotions/reactions
- Implement win conditions and prize distribution
- Add more sophisticated NPC personalities
- Create save/load system for persistent reputation
- Add multiplayer support for multiple hosts

This creates a complete social capital game where your reputation as a fair host directly impacts your ability to run profitable games!
```
