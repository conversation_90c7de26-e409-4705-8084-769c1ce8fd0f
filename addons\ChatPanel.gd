# ChatPanel.gd
# Manages the Chat UI, player input, displaying conversation,
# and CREATES/MANAGES NobodyWhoChat nodes for each survivor.
# Sets system prompt just before each 'say' call.
extends Panel # Changed back to <PERSON> as per your code, ensure CanvasLayer isn't needed for layering

# --- UI Node References ---
@onready var ai_chat_log: RichTextLabel = $PanelContainer/VBoxContainer/AIChat
@onready var chat_input: TextEdit = $PanelContainer/VBoxContainer/ChatInput
@onready var current_npc_label: Label = $PanelContainer/VBoxContainer/CurrentNPCLabel

# --- Node References ---
# *** IMPORTANT: Set this path correctly in the editor or _ready() ***
# This should point to the SINGLE NobodyWhoModel node in your scene.
var nobody_who_model_node_path: NodePath = "NobodyWhoModel" # Default path, ADJUST IF NEEDED
var nobody_who_model_node: NobodyWhoModel = null # Will be assigned in _ready

# Optional: Sampler resource reference if you use one
# var nobody_who_sampler_resource: Resource = preload("res://path/to/your/sampler.tres") # Example

# --- State ---
var npc_chat_nodes: Dictionary = {} # Key: survivor name (String), Value: NobodyWhoChat Node instance
var current_interlocutor: Survivor = null

# --- Constants ---
const NOTIFICATION_VISIBILITY_CHANGED_INT: int = 44 # Use integer value

# --- Initialization ---
func _ready():
	# Get the model node reference
	nobody_who_model_node = get_node_or_null(nobody_who_model_node_path)
	if not is_instance_valid(nobody_who_model_node):
		printerr("FATAL ERROR in ChatPanel: NobodyWhoModel node not found at path: %s! Chat disabled." % nobody_who_model_node_path)
		set_process(false)
		set_physics_process(false)
		set_process_input(false)
	else:
		print("ChatPanel found NobodyWhoModel node.")
		
	# Ensure initial UI state is correct
	update_ui()


# --- Visibility Change Handling ---
func _notification(what):
	if what == NOTIFICATION_VISIBILITY_CHANGED_INT: # Use our defined constant
		if visible:
			print("ChatPanel became visible.")
			# Select appropriate interlocutor when panel appears
			if GameManager and is_instance_valid(GameManager.camp):
				if GameManager.camp.current_state == GameManager.GameState.MORNING_INTERVIEW and \
				   is_instance_valid(GameManager.current_newcomer):
					_set_current_interlocutor(GameManager.current_newcomer)
				else:
					call_deferred("select_first_survivor")
			else:
				_set_current_interlocutor(null)
			
			call_deferred("update_ui")
			if is_instance_valid(chat_input):
				chat_input.call_deferred("grab_focus")
		else:
			print("ChatPanel became hidden.")


# --- UI Update ---
func update_ui():
	if not is_inside_tree(): return
	if is_instance_valid(current_interlocutor):
		current_npc_label.text = "Talking to: %s" % current_interlocutor.name
	else:
		current_npc_label.text = "Talking to: (Nobody Available/Selected)"
		
	if is_instance_valid(chat_input):
		if visible and is_instance_valid(current_interlocutor):
			chat_input.placeholder_text = "Type message to %s..." % current_interlocutor.name
			chat_input.editable = true
		elif visible:
			chat_input.placeholder_text = "Select survivor (Tab)..."
			chat_input.editable = false
		else:
			chat_input.placeholder_text = "Chat disabled."
			chat_input.editable = false


# --- Survivor Selection Logic ---
func get_possible_interlocutors() -> Array[Survivor]:
	var list: Array[Survivor] = []
	if not GameManager or not is_instance_valid(GameManager.camp): return list
	var current_game_state = GameManager.camp.current_state

	if current_game_state == GameManager.GameState.MORNING_INTERVIEW:
		if is_instance_valid(GameManager.current_newcomer):
			list.append(GameManager.current_newcomer)
	elif current_game_state == GameManager.GameState.EVENING_IDLE:
		if GameManager.camp.survivors:
			for s_inst in GameManager.camp.survivors:
				if is_instance_valid(s_inst): list.append(s_inst)
	return list


func select_first_survivor():
	var possible_targets = get_possible_interlocutors()
	var target_survivor: Survivor = null
	if not possible_targets.is_empty():
		if not is_instance_valid(current_interlocutor) or not possible_targets.has(current_interlocutor):
			target_survivor = possible_targets[0]
		else:
			target_survivor = current_interlocutor
	_set_current_interlocutor(target_survivor)


func select_next_survivor():
	var possible_targets = get_possible_interlocutors()
	if possible_targets.size() < 1:
		_set_current_interlocutor(null); return
	if possible_targets.size() == 1:
		if current_interlocutor != possible_targets[0]:
			_set_current_interlocutor(possible_targets[0])
		return

	var current_selected_idx = -1
	if is_instance_valid(current_interlocutor):
		current_selected_idx = possible_targets.find(current_interlocutor)
	
	var next_idx = (current_selected_idx + 1) % possible_targets.size()
	_set_current_interlocutor(possible_targets[next_idx])


func _set_current_interlocutor(survivor_instance: Survivor):
	"""Sets the active interlocutor and ensures their chat node is ready."""
	if current_interlocutor == survivor_instance:
		return
		
	current_interlocutor = survivor_instance
	
	if is_instance_valid(current_interlocutor):
		_prepare_chat_node_for_survivor(current_interlocutor)
	
	_update_after_selection()


func _update_after_selection():
	"""Updates the UI after a new interlocutor is selected (or deselected)."""
	if is_instance_valid(ai_chat_log):
		if is_instance_valid(current_interlocutor):
			ai_chat_log.text = "[center]--- Conversation with %s ---[/center]\n" % current_interlocutor.name
			print("ChatPanel selected: %s" % current_interlocutor.name)
		else:
			ai_chat_log.text = "[center][System: No one selected or available.][/center]\n"
			print("ChatPanel: No one selected.")
	update_ui()


# --- Dynamic Chat Node Management ---
func _prepare_chat_node_for_survivor(survivor_inst: Survivor):
	"""Ensures a NobodyWhoChat node exists for the survivor, creating if needed."""
	if not is_instance_valid(survivor_inst):
		printerr("Prepare Chat Node: Invalid survivor instance."); return
	if not is_instance_valid(nobody_who_model_node):
		printerr("Prepare Chat Node: Model node is invalid!"); return

	var survivor_name = survivor_inst.name
	if npc_chat_nodes.has(survivor_name):
		# Node exists, no need to set system prompt here anymore.
		print("Chat node for %s already exists." % survivor_name)
		return

	# Create and configure a new node
	print("Creating new NobodyWhoChat node for %s" % survivor_name)
	var new_chat_node = NobodyWhoChat.new()
	new_chat_node.name = "ChatNode_" + survivor_name.replace(" ", "_")

	# --- Configure the new node ---
	new_chat_node.model_node = nobody_who_model_node
	# if is_instance_valid(nobody_who_sampler_resource): new_chat_node.sampler = nobody_who_sampler_resource
	# new_chat_node.context_length = 2048
	# new_chat_node.stop_tokens = PackedStringArray(["\n\n", "Leader:", "You:"])
	
	# System prompt is NOT set here anymore. It's set just before say().

	# --- Connect Signals ---
	if new_chat_node.has_signal("response_updated"):
		new_chat_node.response_updated.connect(_on_nobody_who_chat_response_updated)
	if new_chat_node.has_signal("response_finished"):
		new_chat_node.response_finished.connect(_on_nobody_who_chat_response_finished.bind(survivor_name))

	# --- Add to Scene & Store Reference ---
	self.add_child(new_chat_node)
	npc_chat_nodes[survivor_name] = new_chat_node

	# Optionally start worker
	# new_chat_node.start_worker()
	print("Chat node created for %s." % survivor_name)


func cleanup_chat_node_for_survivor(survivor_name: String):
	"""Removes and frees the chat node associated with a removed survivor."""
	if npc_chat_nodes.has(survivor_name):
		print("Cleaning up chat node for removed survivor: %s" % survivor_name)
		var node_to_remove = npc_chat_nodes[survivor_name]
		npc_chat_nodes.erase(survivor_name)
		
		if is_instance_valid(node_to_remove):
			if node_to_remove.is_inside_tree():
				node_to_remove.queue_free()
			else:
				node_to_remove.free()
				
		if is_instance_valid(current_interlocutor) and current_interlocutor.name == survivor_name:
			print("Current interlocutor %s was removed, deselecting." % survivor_name)
			_set_current_interlocutor(null)
	else:
		print("Cleanup requested for %s, but no active chat node found." % survivor_name)


# --- Chat Logic ---
func send_chat_message():
	if not visible: print("ChatPanel: Send chat while hidden."); return
	if not is_instance_valid(current_interlocutor):
		append_to_log("\n[System: Select survivor (Tab).]"); return
	if not is_instance_valid(chat_input): printerr("ChatInput missing!"); return
	
	var user_input_text = chat_input.text.strip_edges()
	if user_input_text.is_empty(): return

	# --- Get the specific chat node ---
	var target_chat_node = npc_chat_nodes.get(current_interlocutor.name)
	if not is_instance_valid(target_chat_node):
		printerr("ERROR: Cannot send message, no valid chat node for %s!" % current_interlocutor.name)
		_prepare_chat_node_for_survivor(current_interlocutor) # Attempt to create it now
		target_chat_node = npc_chat_nodes.get(current_interlocutor.name)
		if not is_instance_valid(target_chat_node):
			append_to_log("\n[System Error: Could not prepare chat session.]")
			return

	# --- UI Updates ---
	chat_input.editable = false; chat_input.text = ""
	var user_display_line = "\n\n[b]Leader:[/b] %s" % user_input_text
	append_to_log(user_display_line)
	
	# --- <<<< NEW: Set System Prompt Just Before Sending >>>> ---
	var system_prompt_text = GameManager.construct_prompt(current_interlocutor)
	target_chat_node.system_prompt = system_prompt_text
	# --- <<<< END NEW >>>> ---

	print("Sending message to %s via their chat node (system prompt updated)..." % current_interlocutor.name)
	append_to_log("\n\n[b]%s[/b]: " % current_interlocutor.name)
	
	# Send ONLY the user message.
	target_chat_node.say(user_input_text)


func append_to_log(text_to_append: String):
	if is_instance_valid(ai_chat_log):
		ai_chat_log.append_text(text_to_append)
		ai_chat_log.call_deferred("scroll_to_line", ai_chat_log.get_line_count() - 1)


# --- Input Handling ---
func _input(event: InputEvent):
	if not visible: return
	if is_instance_valid(chat_input) and chat_input.has_focus() and event.is_action_pressed("ui_text_newline"):
		send_chat_message(); get_viewport().set_input_as_handled()
	elif event.is_action_pressed("switch_survivor"): # Changed from ui_select based on your code
		select_next_survivor(); get_viewport().set_input_as_handled()


# --- LLM Response Handling ---
func _on_nobody_who_chat_response_updated(new_token: String):
	append_to_log(new_token)

# Added bound survivor_name parameter
func _on_nobody_who_chat_response_finished(full_response: String, survivor_name_bound: String):
	print("LLM response finished for bound survivor: %s." % survivor_name_bound)
	
	if is_instance_valid(current_interlocutor) and current_interlocutor.name == survivor_name_bound:
		append_to_log("\n")
		if is_instance_valid(chat_input):
			chat_input.editable = true
			chat_input.grab_focus()
	else:
		print("(Response was for non-active survivor: %s)" % survivor_name_bound)
