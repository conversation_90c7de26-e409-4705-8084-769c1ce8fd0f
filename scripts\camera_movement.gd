# camera_movement.gd
extends Node3D

@export var move_speed = 5.0
@export var rotation_speed = 0.2
@export var zoom_speed = 2
@export_node_path("Node3D") var camera_arm_path

var camera_arm: Node3D

func _ready():
	if camera_arm_path:
		camera_arm = get_node(camera_arm_path)

func _process(delta):
	# Panning
	var input_dir = Input.get_vector("pan_left", "pan_right", "pan_forward", "pan_back")
	var direction = (transform.basis * Vector3(input_dir.x, 0, input_dir.y)).normalized()
	global_position += direction * move_speed * delta

	# Rotation (Hold Middle Mouse Button)
	if Input.is_action_pressed("rotate_camera"):
		rotation_degrees.y -= Input.get_last_mouse_velocity().x * rotation_speed * delta

func _unhandled_input(event):
	# Zooming
	if event is InputEventMouseButton and camera_arm:
		if event.is_action_pressed("zoom_in"):
			camera_arm.position.y -= zoom_speed
		if event.is_action_pressed("zoom_out"):
			camera_arm.position.y += zoom_speed
		# Clamp zoom
		camera_arm.position.z = clamp(camera_arm.position.z, -20, -5)
