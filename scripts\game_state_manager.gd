# scripts/game_state_manager.gd
class_name GameStateManager
extends Node

signal game_event(event: String, data: Dictionary)

var npcs: Array[NPC] = []
var chairs: Array[Node3D] = []
var event_log: Array[String] = []
var game_active: bool = false
var round_number: int = 0

@onready var grid_manager = get_node("../GridManager")

func _ready():
	# Connect to building manager to track chair placement
	var building_manager = get_node("../BuildingManager")
	# We'll need to modify building manager to emit signals

func add_npc(npc: NPC):
	npcs.append(npc)
	npc.action_completed.connect(_on_npc_action_completed)
	npc.chat_message.connect(_on_npc_chat)
	log_event("npc_joined", {"name": npc.npc_name, "position": npc.get_position_grid()})

func remove_npc(npc: NPC):
	npcs.erase(npc)
	log_event("npc_left", {"name": npc.npc_name})

func add_chair(chair: Node3D):
	chairs.append(chair)
	var grid_pos = grid_manager.world_to_map(chair.global_position)
	log_event("host_placed_chair", {"position": Vector2i(grid_pos.x, grid_pos.z)})
	game_event.emit("chair_placed", {"chair": chair})

func remove_chair(chair: Node3D):
	chairs.erase(chair)
	var grid_pos = grid_manager.world_to_map(chair.global_position)
	log_event("host_removed_chair", {"position": Vector2i(grid_pos.x, grid_pos.z)})

func kick_npc(npc: NPC):
	log_event("host_kicked_npc", {"name": npc.npc_name})
	game_event.emit("npc_kicked", {"npc": npc})
	# TODO: Trigger exit interview

func log_event(event_type: String, data: Dictionary = {}):
	var timestamp = Time.get_unix_time_from_system()
	var event_string = event_type
	
	match event_type:
		"host_placed_chair":
			event_string = "Host placed chair at (%d,%d)" % [data.position.x, data.position.y]
		"npc_joined":
			event_string = "%s joined the room at (%d,%d)" % [data.name, data.position.x, data.position.y]
		"npc_moved":
			event_string = "%s moved to (%d,%d)" % [data.name, data.position.x, data.position.y]
		"npc_sat_on_chair":
			event_string = "%s sat on chair at (%d,%d)" % [data.name, data.position.x, data.position.y]
		"host_kicked_npc":
			event_string = "Host kicked %s" % data.name
	
	event_log.append(event_string)
	print("EVENT: ", event_string)
	
	# Keep only last 20 events for LLM context
	if event_log.size() > 20:
		event_log = event_log.slice(-20)

func _on_npc_action_completed(npc: NPC, action: String):
	match action:
		"move_completed":
			log_event("npc_moved", {"name": npc.npc_name, "position": npc.get_position_grid()})
		"sat_on_chair":
			log_event("npc_sat_on_chair", {"name": npc.npc_name, "position": npc.get_position_grid()})

func _on_npc_chat(npc: NPC, message: String):
	log_event("npc_chat", {"name": npc.npc_name, "message": message})

func generate_ascii_map() -> String:
	# Create a simple ASCII representation of the room
	var map_size = 10
	var ascii_map = []
	
	# Initialize empty map
	for y in range(map_size):
		var row = []
		for x in range(map_size):
			row.append(".")
		ascii_map.append(row)
	
	# Place chairs
	for chair in chairs:
		var grid_pos = grid_manager.world_to_map(chair.global_position)
		var x = clamp(grid_pos.x + map_size/2, 0, map_size-1)
		var y = clamp(grid_pos.z + map_size/2, 0, map_size-1)
		ascii_map[y][x] = "C"
	
	# Place NPCs
	for npc in npcs:
		var grid_pos = npc.get_position_grid()
		var x = clamp(grid_pos.x + map_size/2, 0, map_size-1)
		var y = clamp(grid_pos.y + map_size/2, 0, map_size-1)
		if ascii_map[y][x] == ".":
			ascii_map[y][x] = "N"
		elif ascii_map[y][x] == "C":
			ascii_map[y][x] = "S"  # Sitting on chair
	
	# Convert to string
	var result = ""
	for row in ascii_map:
		result += "".join(row) + "\n"
	
	return result

func get_recent_events(count: int = 5) -> Array[String]:
	var start_index = max(0, event_log.size() - count)
	return event_log.slice(start_index)
