# edge.gd (create a new script file)
class_name Edge
extends RefCounted

var p1: Vector3i
var p2: Vector3i

func _init(point1: Vector3i, point2: Vector3i):
	# Sort points to make the edge representation canonical
	if point1.x < point2.x or (point1.x == point2.x and point1.z < point2.z):
		self.p1 = point1
		self.p2 = point2
	else:
		self.p1 = point2
		self.p2 = point1

# We need to override these for dictionary keys to work correctly.
func _hash():
	return hash(p1) + hash(p2)

func _eq(other):
	return other is Edge and self.p1 == other.p1 and self.p2 == other.p2
