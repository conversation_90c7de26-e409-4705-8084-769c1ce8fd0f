# building_manager.gd
extends Node

enum BuildMode { NONE, FLOOR, WALL, FURNITURE, DELETE }

var current_mode = BuildMode.NONE
var selected_furniture_scene: PackedScene

@onready var grid_manager = get_node("../GridManager")
@onready var floors_container = $Floors # Create this node
@onready var walls_container = $Walls   # Create this node
@onready var furniture_container = $Furniture # Create this node

# Dictionaries to store placed objects
var floor_data = {} # Key: Vector3i, Value: Node3D
var wall_data = {}  # Key: String (edge position + direction), Value: Node3D
var furniture_data = {} # Key: Vector3i (origin), Value: Node3D

# Input handling variables
var is_dragging = false
var start_drag_pos: Vector3i
# Note: Removed edge-specific drag variables since walls are now placed with single clicks

func _unhandled_input(_event):
	if not grid_manager.is_mouse_on_grid:
		return

	if current_mode == BuildMode.FLOOR:
		handle_floor_placement()
	if current_mode == BuildMode.WALL:
		handle_wall_placement()
	if current_mode == BuildMode.FURNITURE:
		handle_furniture_placement()
	if current_mode == BuildMode.DELETE:
		handle_deletion()

func set_build_mode(mode: BuildMode):
	current_mode = mode
	print("Build mode set to: ", BuildMode.keys()[mode])

	# Set the appropriate highlighter mode
	# Show edge highlighter for wall mode and delete mode (to see walls being deleted)
	if grid_manager:
		grid_manager.set_highlighter_mode(mode == BuildMode.WALL or mode == BuildMode.DELETE)
	
# This would be called by your UI when a furniture button is clicked
func select_furniture(scene: PackedScene):
	current_mode = BuildMode.FURNITURE
	selected_furniture_scene = scene
	# You would also spawn a preview mesh here

# Call this from _input()
func handle_furniture_placement():
	if selected_furniture_scene == null: return

	# Update preview object position and rotation
	# Check if the target cells have floors and are not occupied by other furniture.
	# Change preview color based on validity.

	if Input.is_action_just_pressed("place_object"): # and is_valid
		var new_furniture = selected_furniture_scene.instantiate()
		furniture_container.add_child(new_furniture)
		new_furniture.global_position = grid_manager.map_to_world(grid_manager.current_grid_pos)
		new_furniture.global_position.y = 0.1 # Floor height
		# Apply rotation from preview
		furniture_data[grid_manager.current_grid_pos] = new_furniture

# Call this from _input() when current_mode == BuildMode.DELETE
func handle_deletion():
	# Highlight the object under the cursor
	if Input.is_action_just_pressed("place_object"):
		var map_pos = grid_manager.current_grid_pos
		var deleted_something = false

		# First, try to delete wall at the closest edge (prioritize walls since we show edge highlighter)
		var edge_key = create_edge_key(grid_manager.current_edge_pos, grid_manager.current_edge_direction)
		if wall_data.has(edge_key):
			wall_data[edge_key].queue_free()
			wall_data.erase(edge_key)
			print("Deleted wall at edge: ", grid_manager.current_edge_pos, " direction: ", grid_manager.current_edge_direction)
			deleted_something = true

		# If no wall was deleted, try furniture
		if not deleted_something and furniture_data.has(map_pos):
			furniture_data[map_pos].queue_free()
			furniture_data.erase(map_pos)
			print("Deleted furniture at: ", map_pos)
			deleted_something = true

		# If no furniture was deleted, try floor
		if not deleted_something and floor_data.has(map_pos):
			floor_data[map_pos].queue_free()
			floor_data.erase(map_pos)
			print("Deleted floor at: ", map_pos)
			deleted_something = true

		# If nothing was deleted, inform the user
		if not deleted_something:
			print("Nothing to delete at this location")

@export var wall_segment_scene: PackedScene

# Call this from _input() when current_mode == BuildMode.WALL
func handle_wall_placement():
	if Input.is_action_just_pressed("place_object"):
		# Single click to place individual wall segments
		place_single_wall_at_edge(grid_manager.current_edge_pos, grid_manager.current_edge_direction)

	# Note: Removed drag functionality for now - walls are placed one edge at a time
	# This makes the placement more precise and intuitive

# New function: Place a single wall at a specific edge
func place_single_wall_at_edge(edge_pos: Vector2, edge_dir: Vector2i):
	# Create an edge identifier for this specific edge
	var edge_key = create_edge_key(edge_pos, edge_dir)

	# Check if wall already exists at this edge
	if wall_data.has(edge_key):
		# Wall exists, remove it (toggle behavior)
		wall_data[edge_key].queue_free()
		wall_data.erase(edge_key)
		print("Removed wall at edge: ", edge_pos, " direction: ", edge_dir)
	else:
		# No wall exists, place one
		var new_wall = wall_segment_scene.instantiate()
		walls_container.add_child(new_wall)

		# Position the wall at the edge
		var world_pos = grid_manager.edge_to_world(edge_pos, edge_dir)
		new_wall.global_position = world_pos
		# Ground the wall at ground level
		new_wall.global_position.y = 0.0

		# Set rotation based on edge direction
		if edge_dir.x != 0:
			# Wall runs along X axis (vertical edge between cells)
			new_wall.rotation.y = PI/2 # 90 degrees
		else:
			# Wall runs along Z axis (horizontal edge between cells)
			new_wall.rotation.y = 0 # 0 degrees

		wall_data[edge_key] = new_wall
		print("Placed wall at edge: ", edge_pos, " direction: ", edge_dir)

# Helper function to create a unique key for edge-based walls
func create_edge_key(edge_pos: Vector2, edge_dir: Vector2i) -> String:
	# Create a unique string key for this edge
	return str(edge_pos.x) + "," + str(edge_pos.y) + ":" + str(edge_dir.x) + "," + str(edge_dir.y)
		

# Note: Removed old path-based wall placement functions since we now use edge-based placement


@export var floor_tile_scene: PackedScene

func handle_floor_placement():
	if Input.is_action_just_pressed("place_object"):
		is_dragging = true
		start_drag_pos = grid_manager.current_grid_pos

	# Update preview while dragging (optional, but good for feedback)
	# We'll skip the detailed preview code for brevity, but you would
	# show highlighted tiles from start_drag_pos to current_grid_pos.

	if Input.is_action_just_released("place_object") and is_dragging:
		is_dragging = false
		place_floor_box(start_drag_pos, grid_manager.current_grid_pos)

func place_floor_box(pos1: Vector3i, pos2: Vector3i):
	var start = Vector2i(min(pos1.x, pos2.x), min(pos1.z, pos2.z))
	var end = Vector2i(max(pos1.x, pos2.x), max(pos1.z, pos2.z))

	for x in range(start.x, end.x + 1):
		for z in range(start.y, end.y + 1):
			var map_pos = Vector3i(x, 0, z)
			if not floor_data.has(map_pos): # Don't place if one already exists
				var new_floor = floor_tile_scene.instantiate()
				floors_container.add_child(new_floor)
				new_floor.global_position = grid_manager.map_to_world(map_pos)
				# Adjust Y so it's slightly above the ground
				new_floor.global_position.y = 0.05
				floor_data[map_pos] = new_floor
