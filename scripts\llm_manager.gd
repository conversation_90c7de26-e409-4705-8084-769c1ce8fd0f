# scripts/llm_manager.gd
class_name LLMManager
extends Node

@onready var game_state = get_node("../GameStateManager")
@onready var nobody_who_chat: NobodyWhoChat = $NobodyWhoChat

var current_npc: NPC = null  # Track which NPC is currently being processed
var action_queue: Array[Dictionary] = []  # Queue for NPC actions

func _ready():
	# Connect nobodywho signals
	if nobody_who_chat:
		nobody_who_chat.response_updated.connect(_on_nobody_who_chat_response_updated)
		nobody_who_chat.response_finished.connect(_on_nobody_who_chat_response_finished)

func generate_npc_action(npc: NPC):
	# Add to queue if currently processing another NPC
	if current_npc != null:
		action_queue.append({"npc": npc})
		print("Queued action request for %s" % npc.npc_name)
		return

	# Process immediately if no one is being processed
	_process_npc_action(npc)

func _process_npc_action(npc: NPC):
	current_npc = npc
	var prompt = create_context_prompt(npc)

	# Reset context to clear memory from previous NPCs
	nobody_who_chat.ResetContext()

	# Set system prompt and send user message
	nobody_who_chat.system_prompt = prompt
	nobody_who_chat.say("What do you want to do?")

func create_context_prompt(npc: NPC) -> String:
	var prompt = """You are %s. You are in a room to play 'Falling Furniture'. 

RULES: When the host places chairs, find an empty one and sit down as fast as possible. The last one standing is out. The host will kick the loser. The winner gets a prize.

CURRENT ROOM STATE:
%s

RECENT EVENTS:
%s

You can take ONE of these actions:
- MOVE_TO(x, y) - Move to grid position
- SIT_ON_CHAIR(x, y) - Sit on chair at position  
- CHAT("message") - Say something
- WAIT - Do nothing this turn

Respond with only: ACTION: [your_action]
""" % [npc.npc_name, game_state.generate_ascii_map(), "\n".join(game_state.get_recent_events())]
	
	return prompt

# Signal handlers for nobodywho
func _on_nobody_who_chat_response_updated(_new_token: String):
	# We don't need to do anything with individual tokens for NPC actions
	pass

func _on_nobody_who_chat_response_finished(full_response: String):
	print("LLM response finished for %s: %s" % [current_npc.npc_name if current_npc else "unknown", full_response])

	if current_npc:
		parse_and_execute_action(current_npc, full_response)
		current_npc = null

		# Process next NPC in queue
		_process_next_in_queue()

func _process_next_in_queue():
	if action_queue.size() > 0:
		var next_request = action_queue.pop_front()
		var next_npc = next_request.npc
		if is_instance_valid(next_npc):
			_process_npc_action(next_npc)
		else:
			# NPC was freed, try next in queue
			_process_next_in_queue()

func parse_and_execute_action(npc: NPC, response: String):
	# Extract action from response
	var action_regex = RegEx.new()
	action_regex.compile(r"ACTION:\s*(.+)")
	var result = action_regex.search(response)
	
	if not result:
		print("No valid action found in LLM response: ", response)
		return
	
	var action = result.get_string(1).strip_edges()
	execute_npc_action(npc, action)

func execute_npc_action(npc: NPC, action: String):
	print("%s wants to: %s" % [npc.npc_name, action])
	
	if action.begins_with("MOVE_TO"):
		var coords = extract_coordinates(action)
		if coords:
			var world_pos = game_state.grid_manager.map_to_world(Vector3i(coords.x, 0, coords.y))
			npc.move_to(world_pos)
	
	elif action.begins_with("SIT_ON_CHAIR"):
		var coords = extract_coordinates(action)
		if coords:
			var chair = find_chair_at_position(coords)
			if chair:
				npc.sit_on_chair(chair)
	
	elif action.begins_with("CHAT"):
		var message = extract_chat_message(action)
		if message:
			npc.show_chat(message)
	
	elif action == "WAIT":
		pass  # Do nothing

func extract_coordinates(action: String) -> Vector2i:
	var coord_regex = RegEx.new()
	coord_regex.compile(r"\((\d+),\s*(\d+)\)")
	var result = coord_regex.search(action)
	
	if result:
		return Vector2i(result.get_string(1).to_int(), result.get_string(2).to_int())
	return Vector2i.ZERO

func extract_chat_message(action: String) -> String:
	var chat_regex = RegEx.new()
	chat_regex.compile(r'CHAT\("([^"]+)"\)')
	var result = chat_regex.search(action)
	
	if result:
		return result.get_string(1)
	return ""

func find_chair_at_position(pos: Vector2i) -> Node3D:
	for chair in game_state.chairs:
		var chair_grid_pos = game_state.grid_manager.world_to_map(chair.global_position)
		if Vector2i(chair_grid_pos.x, chair_grid_pos.z) == pos:
			return chair
	return null
