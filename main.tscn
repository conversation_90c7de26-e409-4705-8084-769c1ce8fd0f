[gd_scene load_steps=21 format=3 uid="uid://cm71uq7rist46"]

[ext_resource type="Script" uid="uid://ikve3i14xs3f" path="res://scripts/camera_movement.gd" id="1_0xm2m"]
[ext_resource type="Shader" uid="uid://bqmnaq5w1jj8x" path="res://assets/ground.gdshader" id="2_h2yge"]
[ext_resource type="Script" uid="uid://88wv3xfo1en4" path="res://scripts/ground_grid.gd" id="3_h2yge"]
[ext_resource type="Script" uid="uid://d3m1i661guf1b" path="res://scripts/grid_manager.gd" id="4_1bvp3"]
[ext_resource type="Script" uid="uid://dov2i814iarvr" path="res://scripts/building_manager.gd" id="5_lquwl"]
[ext_resource type="PackedScene" uid="uid://tht7pqjm3l04" path="res://scenes/floor_tile.tscn" id="6_7mycd"]
[ext_resource type="PackedScene" uid="uid://dj4fuo47wxit5" path="res://scenes/wall_segment.tscn" id="6_272bh"]
[ext_resource type="Script" path="res://scripts/llm_manager.gd" id="7_llm_manager"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_1bvp3"]

[sub_resource type="Sky" id="Sky_lquwl"]
sky_material = SubResource("ProceduralSkyMaterial_1bvp3")

[sub_resource type="Environment" id="Environment_7mycd"]
background_mode = 2
sky = SubResource("Sky_lquwl")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_0xm2m"]
render_priority = 0
shader = ExtResource("2_h2yge")
shader_parameter/grid_color = Color(1, 1, 1, 1)
shader_parameter/base_color = Color(0.2, 0.15, 0.1, 1)
shader_parameter/grid_thickness = 1.0
shader_parameter/grid_scale = Vector2(50, 50)

[sub_resource type="PlaneMesh" id="PlaneMesh_272bh"]
size = Vector2(50, 50)

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_1bvp3"]
data = PackedVector3Array(25, 0, 25, -25, 0, 25, 25, 0, -25, -25, 0, 25, -25, 0, -25, 25, 0, -25)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_1bvp3"]
render_priority = 1
transparency = 1
albedo_color = Color(0.211765, 1, 1, 0.501961)

[sub_resource type="BoxMesh" id="BoxMesh_lquwl"]
material = SubResource("StandardMaterial3D_1bvp3")
size = Vector3(1, 0.01, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_edge"]
render_priority = 1
transparency = 1
albedo_color = Color(1, 0.5, 0, 0.7)

[sub_resource type="BoxMesh" id="BoxMesh_edge"]
material = SubResource("StandardMaterial3D_edge")
size = Vector3(1, 0.02, 0.1)

[sub_resource type="ORMMaterial3D" id="ORMMaterial3D_1bvp3"]
shading_mode = 0
albedo_color = Color(1, 0.390428, 0.573127, 1)

[sub_resource type="ImmediateMesh" id="ImmediateMesh_lquwl"]

[node name="Main" type="Node3D"]

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_7mycd")

[node name="CameraPivot" type="Node3D" parent="."]
script = ExtResource("1_0xm2m")
camera_arm_path = NodePath("CameraArm")

[node name="CameraArm" type="Node3D" parent="CameraPivot"]
transform = Transform3D(1, 0, 0, 0, 0.5, 0.866026, 0, -0.866026, 0.5, 0, 0, 0)

[node name="Camera3D" type="Camera3D" parent="CameraPivot/CameraArm"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 15)

[node name="Ground" type="MeshInstance3D" parent="."]
material_override = SubResource("ShaderMaterial_0xm2m")
mesh = SubResource("PlaneMesh_272bh")
script = ExtResource("3_h2yge")

[node name="GroundCollider" type="StaticBody3D" parent="Ground"]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="Ground/GroundCollider"]
shape = SubResource("ConcavePolygonShape3D_1bvp3")

[node name="GridManager" type="Node3D" parent="."]
script = ExtResource("4_1bvp3")

[node name="GridHighlighter" type="MeshInstance3D" parent="GridManager"]
mesh = SubResource("BoxMesh_lquwl")

[node name="EdgeHighlighter" type="MeshInstance3D" parent="GridManager"]
visible = false
mesh = SubResource("BoxMesh_edge")

[node name="DebugRayLine" type="MeshInstance3D" parent="."]
material_override = SubResource("ORMMaterial3D_1bvp3")
mesh = SubResource("ImmediateMesh_lquwl")

[node name="BuildingManager" type="Node" parent="."]
script = ExtResource("5_lquwl")
wall_segment_scene = ExtResource("6_272bh")
floor_tile_scene = ExtResource("6_7mycd")

[node name="Floors" type="Node" parent="BuildingManager"]

[node name="Walls" type="Node" parent="BuildingManager"]

[node name="Furniture" type="Node" parent="BuildingManager"]

[node name="LLMManager" type="Node" parent="."]
script = ExtResource("7_llm_manager")

[node name="NobodyWhoChat" type="NobodyWhoChat" parent="LLMManager"]

[node name="UI" type="Control" parent="."]
layout_mode = 3
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 0

[node name="PanelContainer" type="PanelContainer" parent="UI"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -78.5
offset_top = -31.0
offset_right = 78.5
grow_horizontal = 2
grow_vertical = 0

[node name="HBoxContainer" type="HBoxContainer" parent="UI/PanelContainer"]
layout_mode = 2
alignment = 1

[node name="ButtonFloor" type="Button" parent="UI/PanelContainer/HBoxContainer"]
layout_mode = 2
text = "Floor"

[node name="ButtonWall" type="Button" parent="UI/PanelContainer/HBoxContainer"]
layout_mode = 2
text = "Wall"

[node name="ButtonFurniture" type="Button" parent="UI/PanelContainer/HBoxContainer"]
layout_mode = 2
text = "Furniture"

[node name="ButtonDelete" type="Button" parent="UI/PanelContainer/HBoxContainer"]
layout_mode = 2
text = "Delete"

[connection signal="pressed" from="UI/PanelContainer/HBoxContainer/ButtonFloor" to="BuildingManager" method="set_build_mode" binds= [1]]
[connection signal="pressed" from="UI/PanelContainer/HBoxContainer/ButtonWall" to="BuildingManager" method="set_build_mode" binds= [2]]
[connection signal="pressed" from="UI/PanelContainer/HBoxContainer/ButtonFurniture" to="BuildingManager" method="set_build_mode" binds= [3]]
[connection signal="pressed" from="UI/PanelContainer/HBoxContainer/ButtonDelete" to="BuildingManager" method="set_build_mode" binds= [4]]
