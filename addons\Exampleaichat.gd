# This is just an example

extends Control

@onready var ai_chat: RichTextLabel = $PanelContainer/VBoxContainer/AIChat
@onready var chat_input: TextEdit = $PanelContainer/VBoxContainer/ChatInput
@onready var nobody_who_chat: NobodyWhoChat = $Miner
@onready var nobody_who_embedding: NobodyWhoEmbedding = $NobodyWhoEmbedding
@onready var peopleEmbeddings = await game_embeddings()

func send_text_to_ai():
	chat_input.editable = false
	var person = await match_sentence(chat_input.text)
	if person != null:
		nobody_who_chat = get_node(person)
		ai_chat.text = "\n---\n You are now talking to " + person + "\n"
		
	ai_chat.text += "\n\nYou: " + chat_input.text + "\n\n"
	nobody_who_chat.say(chat_input.text)

func game_embeddings():
	var sentences = {
		"Farmer": [
			"What are the turnip prices?",
			"Is there any farmer here?",
			"How do I plant that?",
		],
		"Miner": [
			"How deep do I need to mine for coal?",
			"Does someone have a pickaxe?",
			"Where to find coal?",
		],
		"Thief": [
			"Any traders here?",
			"I think I lost my purse.",
			"Items keep disappearing over night."
		],
	}
	
	var embeddings = {"Farmer": [], "Miner": [], "Thief": [],}
	
	for person in sentences:
		for sentence in sentences[person]:
			embeddings[person].append(await nobody_who_embedding.embed(sentence))
	
	return embeddings
	
func match_sentence(sentence: String):
	var maxSimilarity = 0
	var mostSimilarPerson = null
	var inputEmbed = await nobody_who_embedding.embed(sentence)
	
	for person in peopleEmbeddings:
		for embedding in peopleEmbeddings[person]:
			var similarity = nobody_who_embedding.cosine_similarity(inputEmbed, embedding)
			if similarity > maxSimilarity:
				mostSimilarPerson = person
				maxSimilarity = similarity
	
	var similarityThreshold = 0.9
	
	if maxSimilarity > similarityThreshold:
		return mostSimilarPerson
	return null

func _input(event: InputEvent) -> void:
	if (event.is_action("ui_text_newline")):
		send_text_to_ai()

func _on_nobody_who_chat_response_updated(new_token: String) -> void:
	ai_chat.text += new_token


func _on_nobody_who_chat_response_finished(response: String) -> void:
	chat_input.editable = true
	chat_input.text = ""
