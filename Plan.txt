Game Slice Plan: "Social Capital" (Working Title)

Engine: Godot
Style: Habbo-like pixel graphics (isometric perspective)
Core Concept: The player acts as a Game Host, manually setting up and refereeing a game of "Falling Furniture." The NPCs, controlled by LLMs, understand the rules of the game contextually. Their primary interaction loop is based on observing the game state, reacting to it, and retrospectively judging the host's fairness to build a persistent Reputation score.
Phase 1: The Sentient Sandbox

	Goal: Create a room where NPCs, powered by LLMs, can perceive their environment and the game's context, and act upon it.

	Tasks:

		Environment & Movement:

			Render a basic isometric room with a grid-based floor.

			Implement click-to-move for the player character.

			Spawn several NPCs in the room.

		The LLM Context Prompt: Create a system that, for each NPC on a regular interval (e.g., every 5 seconds or after any player action), generates a detailed prompt. This is the core of the game. The prompt must contain:

			Identity & Rules: "You are [NPC Name]. You are in a room to play 'Falling Furniture'. The rules are: when the host places chairs, find an empty one and sit down as fast as possible. The last one standing is out. The host will kick the loser. The winner gets a prize."

			Room State (ASCII Map): A simple text grid showing positions of walls, the player, other NPCs, and furniture. Example: P for Player, N for NPC, C for Chair.

			Event Log: A short, running log of the most recent actions. (e.g., ["Host placed chair at (5,5)", "NPC_Bob moved to (4,5)"]).

		Action Parser:

			Send the generated prompt to the local LLM.

			The LLM should respond with a structured action. Define a simple action format, e.g., ACTION: MOVE_TO(x, y) or ACTION: CHAT("I'm going to win!").

			Write a parser in Godot that reads the LLM's response and triggers the corresponding in-game action (pathfinding to the target, displaying a chat bubble).

	Slice Goal 1: The room feels "alive." NPCs don't just wander randomly; they move with purpose based on the rules they've been given, even if there are no chairs yet (e.g., they might move towards the center of the room in anticipation).

Phase 2: The Host's Toolkit & Emergent Game

	Goal: Empower the player to manually run the game and have the LLMs react to it, creating the core "Falling Furniture" loop.

	Tasks:

		Player as Game Master:

			Implement UI for the player to place furniture (specifically, chairs) into the room from a simple inventory.

			Implement a player ability to "kick" an NPC from the room via a click. This is the primary mechanic for eliminating players from the game.

		Enhance the Event Log: The log passed to the LLMs must now be more robust. Every key action MUST be logged:

			host_placed_chair_at(x,y)

			npc_X_sat_on_chair_at(x,y)

			host_kicked_npc_Y

		LLM's Strategic Reaction: The LLM prompt is now dynamic. As soon as a chair is placed, the LLMs' internal logic (driven by the prompt) will compel them to find a path to it. They will now see events like host_kicked_npc_Y and understand that the game is progressing. The LLMs decide who is fastest; the player decides who to kick. This is where cheating becomes possible.
		
		Test that the LLM controlled NPCs try to get to a free chair.

		Game Finale:

			The game ends when only one NPC is left.

			Implement a simple trading UI for the player to give a Gold prize to the winning NPC. This action must also be logged: host_traded_npc_Z_X_gold.

	Slice Goal 2: The full game of "Falling Furniture" can be played. The player places chairs, NPCs rush to them, the player kicks the loser, and a winner is eventually rewarded. The entire process is driven by the player's manual actions and the LLMs' interpretation of the rules and events.

Phase 3: The Reputation & Consequence Engine

	Goal: Make the game's outcome and the host's actions matter. This phase implements the core progression loop of managing Reputation (Prestige).

	Tasks:

		Global Reputation System: Create a global variable for the player's Host Reputation, displayed on the UI.

		The Exit Interview (Evaluation Prompt): This is a critical, new type of prompt. When an NPC leaves the room (either by being kicked or after winning/losing at the end), send it a final LLM prompt:

			Prompt: "You have just left the game session. Here is the complete event log from your time in the room: [Full Event Log]. Based on this log, please evaluate the host. Was the game fair? Were you cheated? Was the prize appropriate? Respond ONLY with a JSON object containing reputation_change (a number between -10 and +10) and justification (a short sentence)."

		Reputation Update:

			Parse the JSON response from the evaluation prompt.

			Add the reputation_change value to the player's global Host Reputation.

			(Optional) You could store the justification strings to show the player what NPCs think of them.

		Closing the Loop (Reputation's Influence):

			Modify the initial prompt for new NPCs entering the room. Add the host's current reputation: "You are entering a room hosted by [Player Name]. Their current reputation is [Reputation Score]."

			Add the "Pay to Stay" rule to the prompt. "If you are about to be kicked, you can offer the host 1 Gold to stay in the game. Your decision should be based on the host's reputation, the potential prize, and your chances of winning."

			Add a new action for the LLMs: ACTION: OFFER_TRADE(item, amount). When an LLM wants to pay to stay, it uses this action.

	Slice Goal 3: A complete and repeatable gameplay loop exists. The player hosts games to earn Gold, but must balance this against their Reputation. A low reputation will cause NPCs to distrust the player and be unwilling to pay to stay, while a high reputation makes for more exciting and potentially profitable games.
