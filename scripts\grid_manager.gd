# grid_manager.gd
extends Node3D

@export var cell_size = 1.0

var current_grid_pos: Vector3i
var current_edge_pos: Vector2 # New: stores the edge position (cell + edge direction)
var current_edge_direction: Vector2i # New: stores which edge (0,1 = top, 1,0 = right, 0,-1 = bottom, -1,0 = left)
var is_mouse_on_grid = false
var show_edge_highlighter = false # New: controls which highlighter to show

@onready var grid_highlighter = $GridHighlighter # We will create this next
@onready var edge_highlighter = $EdgeHighlighter # New: for highlighting edges
@onready var debug_line_mesh = get_node("/root/Main/DebugRayLine")

func _physics_process(_delta):
	var camera = get_viewport().get_camera_3d()
	if not camera: return

	var mouse_pos = get_viewport().get_mouse_position()

	# --- Ray Calculation ---
	var from = camera.project_ray_origin(mouse_pos)
	var to = from + camera.project_ray_normal(mouse_pos) * 2000

	# --- Raycast Logic ---
	var space_state = get_world_3d().direct_space_state
	var query = PhysicsRayQueryParameters3D.create(from, to)
	query.collision_mask = 2 # Use Layer 2 for the ground

	var result = space_state.intersect_ray(query)

	is_mouse_on_grid = !result.is_empty()

	if is_mouse_on_grid:
		var world_pos = result.position
		current_grid_pos = world_to_map(world_pos)

		# Calculate which edge of the cell the mouse is closest to
		var edge_info = get_closest_edge(world_pos)
		current_edge_pos = edge_info.position
		current_edge_direction = edge_info.direction

		# Update highlighters based on mode
		if show_edge_highlighter:
			# Show edge highlighter (for wall mode)
			if edge_highlighter:
				var edge_world_pos = edge_to_world(current_edge_pos, current_edge_direction)
				edge_world_pos.y += 0.02 # Slightly higher than grid highlighter
				edge_highlighter.global_position = edge_world_pos

				# Rotate the edge highlighter based on edge direction
				if current_edge_direction.x != 0:
					# Vertical edge (runs along X axis)
					edge_highlighter.rotation.y = 0
				else:
					# Horizontal edge (runs along Z axis)
					edge_highlighter.rotation.y = PI/2

				edge_highlighter.visible = true
			if grid_highlighter:
				grid_highlighter.visible = false
		else:
			# Show grid highlighter (for non-wall modes)
			if grid_highlighter:
				var target_pos = map_to_world(current_grid_pos)
				target_pos.y += 0.01 # Add a small vertical offset to prevent Z-fighting with the ground.
				grid_highlighter.global_position = target_pos
				grid_highlighter.visible = true
			if edge_highlighter:
				edge_highlighter.visible = false
	else:
		if grid_highlighter:
			grid_highlighter.visible = false
		if edge_highlighter:
			edge_highlighter.visible = false

func world_to_map(world_pos: Vector3) -> Vector3i:
	var map_pos = Vector3i()
	map_pos.x = floor(world_pos.x / cell_size)
	map_pos.z = floor(world_pos.z / cell_size)
	return map_pos

func map_to_world(map_pos: Vector3i) -> Vector3:
	var world_pos = Vector3()
	world_pos.x = map_pos.x * cell_size + cell_size / 2.0
	world_pos.z = map_pos.z * cell_size + cell_size / 2.0
	return world_pos

# New function: Calculate which edge of a cell the mouse position is closest to
func get_closest_edge(world_pos: Vector3) -> Dictionary:
	# Get the cell position
	var cell_pos = world_to_map(world_pos)
	var cell_center = map_to_world(cell_pos)

	# Calculate relative position within the cell (from -0.5 to 0.5)
	var relative_x = (world_pos.x - cell_center.x) / cell_size
	var relative_z = (world_pos.z - cell_center.z) / cell_size

	# Determine which edge is closest based on which coordinate is closer to the edge
	var abs_x = abs(relative_x)
	var abs_z = abs(relative_z)

	var edge_direction: Vector2i
	var edge_position: Vector2

	if abs_x > abs_z:
		# Closer to left or right edge
		if relative_x > 0:
			# Right edge
			edge_direction = Vector2i(1, 0)
			edge_position = Vector2(cell_pos.x + 0.5, cell_pos.z)
		else:
			# Left edge
			edge_direction = Vector2i(-1, 0)
			edge_position = Vector2(cell_pos.x - 0.5, cell_pos.z)
	else:
		# Closer to top or bottom edge
		if relative_z > 0:
			# Top edge (positive Z)
			edge_direction = Vector2i(0, 1)
			edge_position = Vector2(cell_pos.x, cell_pos.z + 0.5)
		else:
			# Bottom edge (negative Z)
			edge_direction = Vector2i(0, -1)
			edge_position = Vector2(cell_pos.x, cell_pos.z - 0.5)

	return {
		"position": edge_position,
		"direction": edge_direction
	}

# New function: Convert edge position to world coordinates
func edge_to_world(edge_pos: Vector2, _edge_dir: Vector2i) -> Vector3:
	var world_pos = Vector3()
	world_pos.x = edge_pos.x * cell_size
	world_pos.z = edge_pos.y * cell_size
	world_pos.y = 0
	return world_pos

# New function: Set which highlighter to show
func set_highlighter_mode(use_edge_highlighter: bool):
	show_edge_highlighter = use_edge_highlighter
