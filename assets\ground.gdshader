// ground_shader.gdshader
shader_type spatial;

uniform vec4 grid_color : source_color = vec4(1.0, 1.0, 1.0, 1.0);
uniform vec4 base_color : source_color = vec4(0.2, 0.15, 0.1, 1.0);
uniform float grid_thickness : hint_range(0.0, 1) = 1;

// REMOVE the old cell_size uniform
// uniform float cell_size = 1.0;

// ADD this new uniform for scaling
uniform vec2 grid_scale = vec2(1.0, 1.0);

void fragment() {
    // CHANGE this line to use the new grid_scale
    vec2 grid_uv = UV * grid_scale;

    // The rest of the code remains the same
    vec2 grid = abs(fract(grid_uv - 0.5) - 0.5) / fwidth(grid_uv);
    float line = min(grid.x, grid.y);
    float grid_line_alpha = 1.0 - min(line, 1.0);
    
    vec3 final_color = mix(base_color.rgb, grid_color.rgb, grid_line_alpha * step(line, grid_thickness));

    ALBEDO = final_color;
    ALPHA = 1.0;
}